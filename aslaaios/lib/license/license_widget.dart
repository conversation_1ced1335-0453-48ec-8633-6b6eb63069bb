import 'dart:convert';

import 'package:aslaa/constant.dart';
import 'package:aslaa/flutter_flow/flutter_flow_icon_button.dart';
import 'package:aslaa/models/user.dart';
import 'package:aslaa/providers/app_provider.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../flutter_flow/flutter_flow_animations.dart';
import '../flutter_flow/flutter_flow_drop_down.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import '../flutter_flow/flutter_flow_util.dart';
import '../flutter_flow/flutter_flow_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
// import 'package:google_fonts/google_fonts.dart';
import 'package:http/http.dart' as http;
import '../service/payment_service.dart';
import '../widgets/enhanced_payment_dialog.dart';

class LicenseWidget extends StatefulWidget {
  const LicenseWidget({Key? key}) : super(key: key);

  @override
  _LicenseWidgetState createState() => _LicenseWidgetState();
}

class _LicenseWidgetState extends State<LicenseWidget>
    with TickerProviderStateMixin {
  final animationsMap = {
    'rowOnPageLoadAnimation': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        FadeEffect(
          curve: Curves.easeInOut,
          delay: 0.ms,
          duration: 600.ms,
          begin: 0,
          end: 1,
        ),
        ScaleEffect(
          curve: Curves.easeInOut,
          delay: 0.ms,
          duration: 600.ms,
          begin: 0.4,
          end: 1,
        ),
      ],
    ),
  };
  String dropDownValue = "3";
  TextEditingController? expiredController;
  TextEditingController? licenseKeyController;
  final scaffoldKey = GlobalKey<ScaffoldState>();
  String qrcode = "";
  List<dynamic> bankurls = ['www.banck1', 'www.bank2'];
  List<dynamic>? urls;

  // Payment verification state
  final PaymentService _paymentService = PaymentService();
  PaymentStatus _paymentStatus = PaymentStatus.idle;
  double _paymentProgress = 0.0;
  String _paymentMessage = '';
  String? _currentInvoiceId;
  bool _isNavigating = false; // Prevent multiple navigation attempts

  void _setupPaymentListeners() {
    _paymentService.paymentStatusStream.listen((status) {
      if (mounted) {
        setState(() {
          _paymentStatus = status;
        });

        if (status == PaymentStatus.success) {
          _handlePaymentSuccess();
        }
      }
    });

    _paymentService.paymentProgressStream.listen((progress) {
      if (mounted) {
        setState(() {
          _paymentProgress = progress;
        });
      }
    });

    _paymentService.paymentMessageStream.listen((message) {
      if (mounted) {
        setState(() {
          _paymentMessage = message;
        });
      }
    });
  }

  void _logCurrentLicenseStatus() {
    try {
      final appProvider = Provider.of<AppProvider>(context, listen: false);
      if (appProvider.user != null) {
        debugPrint('=== CURRENT LICENSE STATUS ===');
        debugPrint('User ID: ${appProvider.user!.id}');
        debugPrint('Phone: ${appProvider.user!.phoneNumber}');
        debugPrint('License Key: ${appProvider.user!.licenseKey}');
        debugPrint('Expired: ${appProvider.user!.expired}');
        debugPrint('Status: ${appProvider.user!.status}');

        if (appProvider.user!.expired != null &&
            appProvider.user!.expired!.isNotEmpty) {
          try {
            DateTime expiry = DateTime.parse(appProvider.user!.expired!);
            DateTime now = DateTime.now();
            int daysRemaining = expiry.difference(now).inDays;
            debugPrint('Days remaining: $daysRemaining');
            debugPrint('Expiry date: ${expiry.toString().split(' ')[0]}');
          } catch (e) {
            debugPrint('Error parsing expiry date: $e');
          }
        }
        debugPrint('==============================');
      } else {
        debugPrint('No user data available for license status check');
      }
    } catch (e) {
      debugPrint('Error checking license status: $e');
    }
  }

  void _handlePaymentSuccess() {
    debugPrint('PAYMENT_SUCCESS:: _handlePaymentSuccess() called');
    // Prevent multiple navigation attempts
    if (_isNavigating) {
      debugPrint('PAYMENT_SUCCESS:: Already navigating, skipping');
      return;
    }
    _isNavigating = true;
    debugPrint('PAYMENT_SUCCESS:: Starting payment success flow');

    // Use WidgetsBinding to ensure navigation happens after current frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) {
        _isNavigating = false;
        return;
      }

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
              'Payment confirmed! Your license has been extended successfully.'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 3),
        ),
      );

      // Don't close dialogs automatically - let them stay open to avoid navigation issues
      debugPrint(
          'PAYMENT_SUCCESS:: Skipping dialog closure to prevent navigation issues');

      // Handle success with redirect to home page
      debugPrint('PAYMENT_SUCCESS:: About to handle success with redirect');
      debugPrint('PAYMENT_SUCCESS:: Current context mounted: $mounted');
      debugPrint(
          'PAYMENT_SUCCESS:: Current route: ${ModalRoute.of(context)?.settings.name}');

      try {
        // Show success message first
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
                '🎉 Payment confirmed! License extended successfully. Redirecting to home page...'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
          ),
        );

        debugPrint('PAYMENT_SUCCESS:: Success message shown');

        // Refresh user data and then redirect
        Future.delayed(const Duration(milliseconds: 500), () async {
          debugPrint('PAYMENT_SUCCESS:: Future.delayed callback started');
          if (mounted) {
            debugPrint(
                'PAYMENT_SUCCESS:: Widget is mounted, proceeding with refresh');
            try {
              debugPrint(
                  'PAYMENT_SUCCESS:: Refreshing user data after payment');

              // Refresh user data to get updated license information
              final appProvider =
                  Provider.of<AppProvider>(context, listen: false);

              // Use token-based refresh to avoid 2FA issues
              SharedPreferences prefs = await SharedPreferences.getInstance();
              String? token = prefs.getString('token');

              if (token != null) {
                debugPrint('PAYMENT_SUCCESS:: Refreshing user data with token');
                await appProvider.loginWithToken(token);
                debugPrint(
                    'PAYMENT_SUCCESS:: User data refreshed successfully');

                // Log updated license status
                _logCurrentLicenseStatus();
              }

              // Small delay to ensure data is updated
              await Future.delayed(const Duration(milliseconds: 300));

              debugPrint(
                  'PAYMENT_SUCCESS:: Redirecting to main controller page with updated data');

              // Simple and reliable navigation to main controller
              debugPrint(
                  'PAYMENT_SUCCESS:: Starting simple navigation to main');

              // Add a small delay to ensure user data is fully updated
              await Future.delayed(const Duration(milliseconds: 500));

              // Check authentication state before navigation
              final currentAppProvider =
                  Provider.of<AppProvider>(context, listen: false);
              debugPrint(
                  'PAYMENT_SUCCESS:: Checking auth state - User: ${currentAppProvider.user != null}');
              debugPrint(
                  'PAYMENT_SUCCESS:: User ID: ${currentAppProvider.user?.id}');
              debugPrint(
                  'PAYMENT_SUCCESS:: User phone: ${currentAppProvider.user?.phoneNumber}');

              // Use the most reliable navigation method
              try {
                debugPrint(
                    'PAYMENT_SUCCESS:: Navigating directly to main controller');
                context.go('/main');
                debugPrint(
                    'PAYMENT_SUCCESS:: Navigation completed successfully');
              } catch (e) {
                debugPrint('PAYMENT_SUCCESS:: Direct navigation failed: $e');

                // Fallback: try pushReplacement
                try {
                  context.pushReplacement('/main');
                  debugPrint(
                      'PAYMENT_SUCCESS:: Fallback navigation successful');
                } catch (e2) {
                  debugPrint('PAYMENT_SUCCESS:: All navigation failed: $e2');
                }
              }
            } catch (e) {
              debugPrint('PAYMENT_SUCCESS:: Error during refresh/redirect: $e');
              // Emergency fallback - just try to navigate somewhere safe
              try {
                context.go('/main');
              } catch (e2) {
                debugPrint(
                    'PAYMENT_SUCCESS:: Emergency navigation failed: $e2');
              }
            }
          } else {
            debugPrint(
                'PAYMENT_SUCCESS:: Widget not mounted, skipping refresh');
          }
        });

        debugPrint('PAYMENT_SUCCESS:: Redirect scheduled');
      } catch (e) {
        debugPrint('PAYMENT_SUCCESS:: Error in success flow: $e');
      }

      _isNavigating = false; // Reset flag immediately
    });
  }

  Uint8List convertBase64Image(String base64String) {
    return Base64Decoder().convert(base64String.split(',').last);
  }

  Future requestLicense(User user) async {
    try {
      setState(() {
        _paymentStatus = PaymentStatus.idle;
      });

      SharedPreferences _prefs = await SharedPreferences.getInstance();
      String? token = _prefs.getString('token');

      // Check if user is properly authenticated
      final appProvider = Provider.of<AppProvider>(context, listen: false);
      bool hasValidToken = token != null && token.isNotEmpty;
      bool hasValidUser =
          appProvider.user != null && appProvider.user!.id.isNotEmpty;

      if (!hasValidToken || !hasValidUser) {
        _showAuthenticationError();
        debugPrint(
            'LicenseWidget:: Missing authentication - Token: ${hasValidToken ? "present" : "missing"}, User: ${hasValidUser ? "present" : "missing"}');
        return;
      }

      // Additional check: if user has 2FA enabled, make sure they're fully authenticated
      if (appProvider.user!.twoFactorEnabled) {
        debugPrint(
            'LicenseWidget:: User has 2FA enabled, checking if fully authenticated');
        // For 2FA users, we need to be extra careful about authentication state
        // The token should be valid and the user should be fully logged in
        if (token != appProvider.user!.token) {
          debugPrint('LicenseWidget:: Token mismatch detected for 2FA user');
          _showAuthenticationError();
          return;
        }
      }

      // Use token from preferences, fallback to user.token
      String authToken = token.isNotEmpty ? token : user.token;
      double totalCost = (5000 * int.parse(dropDownValue)).toDouble();

      // Store payment cost for later use
      await _paymentService.storePaymentCost(totalCost);

      Map<String, String> headers = {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $authToken',
      };

      final response = await http.post(
          Uri.parse('$API_HOST/api/license/extend-license'),
          headers: headers,
          body: jsonEncode({"totalCost": totalCost}));

      if (response.statusCode == 200) {
        Map<String, dynamic> result = jsonDecode(response.body);
        debugPrint('License extension response: $result');

        if (result['data'] != null) {
          Map<String, dynamic> invoice = result['data']["bankList"];
          urls = invoice['urls'];
          qrcode = invoice['qr_image'];

          // Get invoice ID for payment verification
          String? invoiceId = result['data']['invoice'];
          _currentInvoiceId = invoiceId;

          debugPrint('Invoice ID: $invoiceId');
          debugPrint('Bank URLs: $urls');

          // Show enhanced payment dialog with automatic verification
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => EnhancedPaymentDialog(
              qrCode: qrcode,
              bankUrls: urls ?? [],
              invoiceId: invoiceId,
              onPaymentSuccess: _handlePaymentSuccess,
              onClose: () {
                _paymentService.stopPaymentCheck();
              },
            ),
          );

          // Start automatic payment checking if invoice ID is available
          if (invoiceId != null) {
            _paymentService.startPaymentCheck(invoiceId);
          }
        }
      } else {
        _showErrorMessage(
            'Failed to initiate license extension. Please try again.');
      }
    } catch (error) {
      debugPrint('License extension error: $error');
      _showErrorMessage(
          'Failed to initiate license extension. Please try again.');
    }
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showAuthenticationError() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Authentication Required',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            const Text(
                'Please complete login and 2FA verification (if enabled) before extending your license.'),
            const SizedBox(height: 8),
            Row(
              children: [
                TextButton(
                  onPressed: () {
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                    // Navigate to login page
                    context.go('/login');
                  },
                  child: const Text(
                    'Go to Login',
                    style: TextStyle(
                        color: Colors.white, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
          ],
        ),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 6),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Future<bool> _checkAuthentication() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString('token');

      // Check if token exists and user is logged in
      final appProvider = Provider.of<AppProvider>(context, listen: false);
      bool hasValidToken = token != null && token.isNotEmpty;
      bool hasValidUser =
          appProvider.user != null && appProvider.user!.id.isNotEmpty;

      // Additional check for 2FA users
      bool is2FACompliant = true;
      if (hasValidUser && appProvider.user!.twoFactorEnabled) {
        // For 2FA users, ensure the stored token matches the user's current token
        is2FACompliant = token == appProvider.user!.token;
        debugPrint(
            'LicenseWidget:: 2FA user detected - Token match: $is2FACompliant');
      }

      bool isAuthenticated = hasValidToken && hasValidUser && is2FACompliant;

      debugPrint(
          'LicenseWidget:: Authentication check - Token: ${hasValidToken ? "present" : "missing"}, User: ${hasValidUser ? "present" : "missing"}, 2FA Compliant: $is2FACompliant, Final: $isAuthenticated');

      return isAuthenticated;
    } catch (e) {
      debugPrint('Error checking authentication: $e');
      return false;
    }
  }

  @override
  void initState() {
    super.initState();
    _setupPaymentListeners();

    // Debug authentication state on page load
    Future.delayed(const Duration(milliseconds: 500), () async {
      bool isAuth = await _checkAuthentication();
      debugPrint('LicenseWidget:: Initial authentication state: $isAuth');

      // Also log current license status
      _logCurrentLicenseStatus();
    });

    AppProvider authProvider = Provider.of<AppProvider>(context, listen: false);

    expiredController = TextEditingController();
    licenseKeyController = TextEditingController();

    // Check if user exists before accessing properties
    if (authProvider.user != null) {
      // Set license key to "888-888-888-888" if empty, otherwise use existing value
      String licenseKey = authProvider.user!.licenseKey;
      licenseKeyController?.text =
          licenseKey.isEmpty ? "888-888-888-888" : licenseKey;
      expiredController?.text = authProvider.user!.expired.substring(0, 10);
    } else {
      // Default values if user is null
      licenseKeyController?.text = "888-888-888-888";
      expiredController?.text = "";
    }
  }

  @override
  void dispose() {
    _paymentService.dispose();
    _isNavigating = false; // Reset navigation flag

    // Don't clear payment success flag here - let it expire naturally
    // This allows navigation to complete before flag is cleared

    expiredController?.dispose();
    licenseKeyController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    AppProvider authProvider = Provider.of<AppProvider>(context);
    User? user = authProvider.authClient;

    // If user is null (after logout), redirect to login
    if (user == null) {
      print('LICENSE:: User is null, redirecting to login');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.go('/');
      });
      return Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      key: scaffoldKey,
      backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
      body: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(0, 60, 0, 24),
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image.asset(
                              'assets/images/Porsche-Taycan-Transparent-PNG.png',
                              width: 240,
                              height: 160,
                              fit: BoxFit.cover,
                            ),
                          ],
                        ).animateOnPageLoad(
                            animationsMap['rowOnPageLoadAnimation']!),
                      ),
                      Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(24, 0, 24, 0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Padding(
                              padding:
                                  EdgeInsetsDirectional.fromSTEB(0, 12, 0, 8),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  Text(
                                    FFLocalizations.of(context).getText(
                                      'p1qci7qz' /* License Information */,
                                    ),
                                    style: FlutterFlowTheme.of(context)
                                        .title1
                                        .override(
                                          fontFamily: 'Roboto',
                                          fontSize: 28,
                                          fontWeight: FontWeight.normal,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                            Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    FlutterFlowIconButton(
                                      borderColor: Colors.transparent,
                                      borderRadius: 30,
                                      borderWidth: 1,
                                      buttonSize: 60,
                                      icon: Icon(
                                        Icons.refresh,
                                        color: FlutterFlowTheme.of(context)
                                            .primaryText,
                                        size: 30,
                                      ),
                                      onPressed: () {
                                        authProvider.reload();
                                      },
                                    ),
                                    Text(
                                      // FFLocalizations.of(context).getText(
                                      //   'w6fbmsvd' /* Trial Mode */,
                                      // ),
                                      user.status.capitalize(),
                                      style: FlutterFlowTheme.of(context)
                                          .bodyText2,
                                    ),
                                  ],
                                ),
                                Text(
                                  // FFLocalizations.of(context).getText(
                                  //   '9vcdikms' /* 28 days remain */,
                                  // ),
                                  '${(user.remainDays / 3600 / 24 / 1000).round()} D',
                                  style: FlutterFlowTheme.of(context).bodyText2,
                                ),
                              ],
                            ),
                            Column(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      0, 20, 0, 0),
                                  child: TextFormField(
                                    controller: licenseKeyController,
                                    readOnly: true,
                                    obscureText: false,
                                    decoration: InputDecoration(
                                      labelText:
                                          FFLocalizations.of(context).getText(
                                        'w392izvc' /* License Number */,
                                      ),
                                      hintStyle: FlutterFlowTheme.of(context)
                                          .bodyText2,
                                      enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Color(0x00000000),
                                          width: 1,
                                        ),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Color(0x00000000),
                                          width: 1,
                                        ),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      errorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Color(0x00000000),
                                          width: 1,
                                        ),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      focusedErrorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Color(0x00000000),
                                          width: 1,
                                        ),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      filled: true,
                                      fillColor: FlutterFlowTheme.of(context)
                                          .secondaryBackground,
                                    ),
                                    style: FlutterFlowTheme.of(context)
                                        .bodyText1
                                        .override(
                                          fontFamily: 'Roboto',
                                          fontWeight: FontWeight.normal,
                                        ),
                                  ),
                                ),
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      0, 20, 0, 0),
                                  child: TextFormField(
                                    controller: expiredController,
                                    readOnly: true,
                                    obscureText: false,
                                    decoration: InputDecoration(
                                      labelText:
                                          FFLocalizations.of(context).getText(
                                        '889porb6' /* Expired Date */,
                                      ),
                                      hintStyle: FlutterFlowTheme.of(context)
                                          .bodyText2,
                                      enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Color(0x00000000),
                                          width: 1,
                                        ),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Color(0x00000000),
                                          width: 1,
                                        ),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      errorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Color(0x00000000),
                                          width: 1,
                                        ),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      focusedErrorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Color(0x00000000),
                                          width: 1,
                                        ),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      filled: true,
                                      fillColor: FlutterFlowTheme.of(context)
                                          .secondaryBackground,
                                    ),
                                    style: FlutterFlowTheme.of(context)
                                        .bodyText1
                                        .override(
                                          fontFamily: 'Roboto',
                                          fontWeight: FontWeight.normal,
                                        ),
                                  ),
                                ),
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      0, 20, 0, 0),
                                  child: FlutterFlowDropDown<String>(
                                    // Use `initialOption` to set the default value
                                    initialOption:
                                        dropDownValue, // Set initial value here
                                    options: [
                                      '1',
                                      '2',
                                      '3',
                                      '6',
                                      '8',
                                      '12',
                                      '36'
                                    ],
                                    optionLabels: [
                                      '1 сар', // 1 month in Mongolian
                                      '2 сар', // 2 months in Mongolian
                                      '3 сар', // 3 months in Mongolian
                                      '6 сар', // 6 months in Mongolian
                                      '8 сар', // 8 months in Mongolian
                                      '1 жил', // 1 year in Mongolian
                                      FFLocalizations.of(context)
                                          .getText('4cwrsf4f' /* Forever */),
                                    ],
                                    onChanged: (val) {
                                      setState(() {
                                        dropDownValue =
                                            val!; // Update the dropDownValue when user selects a new option
                                        print(
                                            '$val'); // Debugging: Print the selected value
                                      });
                                    },
                                    width: MediaQuery.of(context).size.width,
                                    height: 50,
                                    textStyle: FlutterFlowTheme.of(context)
                                        .bodyText1
                                        .override(
                                          fontFamily: 'Roboto',
                                          color: FlutterFlowTheme.of(context)
                                              .primaryText,
                                          fontWeight: FontWeight.normal,
                                        ),
                                    hintText: FFLocalizations.of(context)
                                        .getText(
                                            'dvy5mz8z' /* Please select... */),
                                    fillColor: FlutterFlowTheme.of(context)
                                        .secondaryBackground,
                                    elevation: 2,
                                    borderColor: Colors.transparent,
                                    borderWidth: 0,
                                    borderRadius: 8,
                                    margin: EdgeInsetsDirectional.fromSTEB(
                                        12, 4, 12, 4),
                                    hidesUnderline: true,
                                  ),
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Align(
                                      alignment: AlignmentDirectional(1, 0),
                                      child: Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            0, 20, 0, 0),
                                        child: Text(
                                          FFLocalizations.of(context).getText(
                                            '71e11p75' /* Total Price:  */,
                                          ),
                                          textAlign: TextAlign.end,
                                          style: FlutterFlowTheme.of(context)
                                              .bodyText1
                                              .override(
                                                fontFamily: 'Roboto',
                                                fontWeight: FontWeight.normal,
                                              ),
                                        ),
                                      ),
                                    ),
                                    Align(
                                      alignment: AlignmentDirectional(1, 0),
                                      child: Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            0, 20, 0, 0),
                                        child: Text(
                                          // FFLocalizations.of(context).getText(
                                          //   '623388kx' /* 5,000 */,
                                          // ),
                                          '${getFormatedNumber(5000 * int.parse(dropDownValue))}',
                                          textAlign: TextAlign.end,
                                          style: FlutterFlowTheme.of(context)
                                              .bodyText1
                                              .override(
                                                fontFamily: 'Roboto',
                                                fontWeight: FontWeight.normal,
                                              ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                // Authentication Warning
                                FutureBuilder<bool>(
                                  future: _checkAuthentication(),
                                  builder: (context, snapshot) {
                                    if (snapshot.hasData && !snapshot.data!) {
                                      return Container(
                                        margin: EdgeInsetsDirectional.fromSTEB(
                                            0, 16, 0, 16),
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            16, 12, 16, 12),
                                        decoration: BoxDecoration(
                                          color: Colors.orange.shade50,
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          border: Border.all(
                                              color: Colors.orange.shade200),
                                        ),
                                        child: Row(
                                          children: [
                                            Icon(Icons.warning,
                                                color: Colors.orange.shade600),
                                            SizedBox(width: 12),
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    'Authentication Required',
                                                    style: TextStyle(
                                                      color: Colors
                                                          .orange.shade700,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                  SizedBox(height: 4),
                                                  Text(
                                                    'Please complete login and 2FA verification (if enabled) to extend your license.',
                                                    style: TextStyle(
                                                      color: Colors
                                                          .orange.shade700,
                                                      fontSize: 12,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            TextButton(
                                              onPressed: () =>
                                                  context.go('/login'),
                                              child: Text(
                                                'Login',
                                                style: TextStyle(
                                                  color: Colors.orange.shade700,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    }
                                    return SizedBox.shrink();
                                  },
                                ),
                                // Payment Status Indicator
                                if (_paymentStatus == PaymentStatus.checking)
                                  Container(
                                    margin: EdgeInsetsDirectional.fromSTEB(
                                        0, 16, 0, 16),
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        16, 12, 16, 12),
                                    decoration: BoxDecoration(
                                      color: Colors.blue.shade50,
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                          color: Colors.blue.shade200),
                                    ),
                                    child: Column(
                                      children: [
                                        Row(
                                          children: [
                                            SizedBox(
                                              width: 20,
                                              height: 20,
                                              child: CircularProgressIndicator(
                                                strokeWidth: 2,
                                                valueColor:
                                                    AlwaysStoppedAnimation<
                                                        Color>(Colors.blue),
                                              ),
                                            ),
                                            SizedBox(width: 12),
                                            Expanded(
                                              child: Text(
                                                _paymentMessage.isNotEmpty
                                                    ? _paymentMessage
                                                    : 'Automatically checking payment status...',
                                                style: TextStyle(
                                                  color: Colors.blue.shade700,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(height: 8),
                                        LinearProgressIndicator(
                                          value: _paymentProgress / 100,
                                          backgroundColor: Colors.blue.shade100,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                  Colors.blue.shade600),
                                        ),
                                        SizedBox(height: 4),
                                        Text(
                                          'Progress: ${_paymentProgress.toStringAsFixed(0)}%',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.blue.shade600,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                if (_paymentStatus == PaymentStatus.success)
                                  Container(
                                    margin: EdgeInsetsDirectional.fromSTEB(
                                        0, 16, 0, 16),
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        16, 12, 16, 12),
                                    decoration: BoxDecoration(
                                      color: Colors.green.shade50,
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                          color: Colors.green.shade200),
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(Icons.check_circle,
                                            color: Colors.green.shade600),
                                        SizedBox(width: 12),
                                        Expanded(
                                          child: Text(
                                            'Payment confirmed! License extended successfully.',
                                            style: TextStyle(
                                              color: Colors.green.shade700,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                if (_paymentStatus == PaymentStatus.timeout)
                                  Container(
                                    margin: EdgeInsetsDirectional.fromSTEB(
                                        0, 16, 0, 16),
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        16, 12, 16, 12),
                                    decoration: BoxDecoration(
                                      color: Colors.orange.shade50,
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                          color: Colors.orange.shade200),
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(Icons.access_time,
                                            color: Colors.orange.shade600),
                                        SizedBox(width: 12),
                                        Expanded(
                                          child: Text(
                                            'Payment verification timed out. Please check manually.',
                                            style: TextStyle(
                                              color: Colors.orange.shade700,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      0, 32, 0, 32),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceAround,
                                    children: [
                                      Expanded(
                                        child: Padding(
                                          padding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  0, 0, 4, 0),
                                          child: FFButtonWidget(
                                            onPressed: () async {
                                              context.pushNamed('dashboard');
                                            },
                                            text: FFLocalizations.of(context)
                                                .getText(
                                              'e3bahb30' /* Go Dashboard */,
                                            ),
                                            options: FFButtonOptions(
                                              width: 130,
                                              height: 40,
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .secondaryBackground,
                                              textStyle: FlutterFlowTheme.of(
                                                      context)
                                                  .subtitle2
                                                  .override(
                                                    fontFamily: 'Roboto',
                                                    color: FlutterFlowTheme.of(
                                                            context)
                                                        .primaryText,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                              borderSide: BorderSide(
                                                color: Colors.transparent,
                                                width: 1,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                            ),
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        child: Padding(
                                          padding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  4, 0, 0, 0),
                                          child: FutureBuilder<bool>(
                                            future: _checkAuthentication(),
                                            builder: (context, authSnapshot) {
                                              bool isAuthenticated =
                                                  authSnapshot.data ?? false;
                                              bool isProcessing =
                                                  _paymentStatus ==
                                                      PaymentStatus.checking;

                                              return FFButtonWidget(
                                                onPressed: (!isAuthenticated ||
                                                        isProcessing)
                                                    ? null
                                                    : () async {
                                                        await requestLicense(
                                                            user);
                                                        print(
                                                            'Button pressed ...');
                                                      },
                                                text: !isAuthenticated
                                                    ? 'Login Required'
                                                    : isProcessing
                                                        ? 'Processing...'
                                                        : 'Сунгах', // Extend in Mongolian
                                                options: FFButtonOptions(
                                                  width: 130,
                                                  height: 40,
                                                  color: !isAuthenticated
                                                      ? Colors.grey
                                                      : FlutterFlowTheme.of(
                                                              context)
                                                          .secondaryColor,
                                                  textStyle: FlutterFlowTheme
                                                          .of(context)
                                                      .subtitle2
                                                      .override(
                                                        fontFamily: 'Roboto',
                                                        color:
                                                            FlutterFlowTheme.of(
                                                                    context)
                                                                .primaryText,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                      ),
                                                  borderSide: BorderSide(
                                                    color: Colors.transparent,
                                                    width: 1,
                                                  ),
                                                  borderRadius:
                                                      BorderRadius.circular(20),
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
