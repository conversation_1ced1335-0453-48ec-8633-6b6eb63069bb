const mongoose = require("mongoose");
const Schema = mongoose.Schema;
// Create Schema
const LicenseSchema = new Schema({
    owner: {
        type: mongoose.Types.ObjectId,

    },
    type: {
        type: String,
        default: "monthly",
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    expired: {
        type: Date,
        default: new Date(Date.now() + 30 * 3600 * 24 * 1000)
    },
    status: {
        type: String,
        default: "active",
    },
    licenseKey: {
        type: String,
        default: ""
    },
    cost: {
        type: Number,
        default: 0,
    },
    invoice: {
        type: String,
        default: ""
    },
    realInvoice:{
        type:String,
        default:""
    }
});
module.exports = License = mongoose.model("license", LicenseSchema);