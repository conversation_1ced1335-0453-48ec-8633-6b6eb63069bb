(this.webpackJsonpclient=this.webpackJsonpclient||[]).push([[6],{154:function(e,t,n){"use strict";n.d(t,"a",(function(){return c}));var i=n(124),a=n(180);function c(e,t,n,c){const r=Object(i.a)(),s=Object(a.a)(r.breakpoints.up(t)),o=Object(a.a)(r.breakpoints.down(t)),l=Object(a.a)(r.breakpoints.between(n,c)),d=Object(a.a)(r.breakpoints.only(t));return"up"===e?s:"down"===e?o:"between"===e?l:"only"===e?d:null}},174:function(e,t,n){"use strict";n.d(t,"a",(function(){return j})),n.d(t,"b",(function(){return b}));var i=n(8),a=n(0),c=n(36),r=n(332);n(188);const s=e=>{e?(localStorage.setItem("accessToken",e),c.a.defaults.headers.common.Authorization="Bearer ".concat(e)):(localStorage.removeItem("accessToken"),delete c.a.defaults.headers.common.Authorization)};var o=n(2);const l={isAuthenticated:!1,isInitialized:!1,user:null},d={INITIALIZE:(e,t)=>{const{isAuthenticated:n,user:a}=t.payload;return Object(i.a)(Object(i.a)({},e),{},{isAuthenticated:n,isInitialized:!0,user:a})},OTPFINAL:(e,t)=>{const{final:n}=t.payload;return Object(i.a)(Object(i.a)({},e),{},{isAuthenticated:!1,isInitialized:!0,final:n,user:null})},LOGINED:(e,t)=>{const{user:n}=t.payload;return Object(i.a)(Object(i.a)({},e),{},{isAuthenticated:!0,user:n})},LOGOUT:e=>Object(i.a)(Object(i.a)({},e),{},{isAuthenticated:!1,final:null,user:null})},u=(e,t)=>d[t.type]?d[t.type](e,t):e,j=Object(a.createContext)(Object(i.a)(Object(i.a)({},l),{},{method:"jwt",login:()=>Promise.resolve(),logout:()=>Promise.resolve(),initialize:()=>Promise.resolve()}));function b(e){let{children:t}=e;const[n,d]=Object(a.useReducer)(u,l),b=async()=>{try{const e=window.localStorage.getItem("accessToken");if(e&&(e=>{if(!e)return!1;const t=Object(r.a)(e),n=Date.now()/1e3;return t.exp>n})(e)){s(e);const t=await c.a.get("/api/auth/my-account"),{user:n}=t.data;d({type:"INITIALIZE",payload:{isAuthenticated:!0,user:n}})}else d({type:"INITIALIZE",payload:{isAuthenticated:!1,user:null}})}catch(e){console.error(e),d({type:"INITIALIZE",payload:{isAuthenticated:!1,user:null}})}};Object(a.useEffect)((()=>{console.log("--------------iniitalize passport-------------------"),b()}),[]);return Object(o.jsxs)(j.Provider,{value:Object(i.a)(Object(i.a)({},n),{},{method:"jwt",login:async e=>{const t=await c.a.post("/api/auth/login",{phoneNumber:e});if(t.data.pinVerify)return"pincode";const{token:n,user:i}=t.data;if(n)return"inactive"===i.status?"inactive":(s(n),d({type:"LOGINED",payload:{user:i}}),"navigate");try{return window.phoneNumber=e,"otp"}catch(a){return console.log(a),"otp"}},logout:async()=>{try{s(null),d({type:"LOGOUT"})}catch(e){console.log(e)}},otpVerify:async(e,t)=>{try{const n=window.phoneNumber,i=await c.a.post("/api/auth/verifyOtp",{phoneNumber:n,otp:e}),{success:a}=i.data;if(a){const e=await c.a.post("/api/auth/register",{phoneNumber:n});if(200===e.status){const{token:n,user:i}=e.data;s(n),d({type:"LOGINED",payload:{user:i}}),t({success:!0})}}else t({success:!1,err:"unmathed otpcode"})}catch(n){t({success:!1,err:"otp response err"})}},codeVerify:async(e,t)=>{try{const n=await c.a.post("/api/auth/pincode",{phoneNumber:e,pinCode:t});if(n.data.requiresTwoFactor)return{success:n.data.success,requiresTwoFactor:!0,message:n.data.message,phoneNumber:n.data.phoneNumber};const{token:i,user:r}=n.data;if(!i)try{return window.phoneNumber=e,{success:!1,message:"pin code verification error"}}catch(a){return{success:!1,message:"pin code verification error"}}return"inactive"===r.status?{success:!1,message:"Your account is inactive. Please contact with administrator"}:(s(i),d({type:"LOGINED",payload:{user:r}}),{success:!0,message:"verification successfully"})}catch(r){var n,i;return console.error("Pin code verification error:",r),{success:!1,message:(null===(n=r.response)||void 0===n||null===(i=n.data)||void 0===i?void 0:i.message)||"Pin code verification error"}}},verify2FA:async function(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{const i=await c.a.post("/api/2fa/verify",{phoneNumber:e,token:t,isBackupCode:n});if(console.log("2FA verify response:",i.data),200===i.data.status){const t=await c.a.post("/api/auth/complete-login-2fa",{phoneNumber:e});if(console.log("Complete login response:",t.data),t.data.success){const{token:e,user:n}=t.data;return s(e),d({type:"LOGINED",payload:{user:n}}),{success:!0}}}return{success:!1,message:i.data.message||"Verification failed"}}catch(r){var i,a;return console.error("2FA verification error:",r),{success:!1,message:(null===(i=r.response)||void 0===i||null===(a=i.data)||void 0===a?void 0:a.message)||"Verification failed"}}},initialize:b}),children:[" ",t," "]})}},237:function(e,t,n){"use strict";n.d(t,"b",(function(){return i.a})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return c}));var i=n(245);const a={MOBILE_HEIGHT:50,MAIN_DESKTOP_HEIGHT:70};n(246);const c=5e3},240:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var i=n(8),a=n(43),c=n(529),r=n(2);function s(e){let{disabledLink:t=!1,sx:n}=e;const s=Object(r.jsx)(c.a,{sx:Object(i.a)({width:60,height:40},n),children:Object(r.jsx)("img",{width:"100%",height:"100%",src:"/logo/logo.png",alt:"logo"})});return t?Object(r.jsxs)(r.Fragment,{children:[" ",s," "]}):Object(r.jsxs)(a.b,{to:"/home",children:[" ",s," "]})}},244:function(e,t,n){"use strict";n.d(t,"b",(function(){return o}));var i=n(108),a=n(36),c=n(90);const r=Object(i.b)({name:"notification",initialState:{isLoading:!1,error:null,notifications:[]},reducers:{startLoading(e){e.isLoading=!0},hasError(e,t){e.error=t.payload,e.isLoading=!1},setNotifications(e,t){e.isLoading=!1,e.notifications=t.payload}}});t.a=r.reducer;const{setNotifications:s}=r.actions;function o(){return async()=>{Object(c.a)(r.actions.startLoading());try{const e=await a.a.get("/api/log/sim-status");Object(c.a)(r.actions.setNotifications(e.data.data))}catch(e){Object(c.a)(r.actions.hasError(e))}}}},245:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));const i="https://www.aslaa.mn/"},246:function(e,t,n){"use strict";n.d(t,"b",(function(){return r}));var i=n(331),a=n(217);n.d(t,"a",(function(){return a.b}));const c=Object(i.a)({apiKey:"AIzaSyDA_x4YElOAVmT4rS3B-xcmCzhvefDTOrI",authDomain:"rccdemo-41279.firebaseapp.com",projectId:"rccdemo-41279",storageBucket:"rccdemo-41279.appspot.com",messagingSenderId:"963013875719",appId:"1:963013875719:web:f9511f343bceb59b06f2a2"}),r=Object(a.a)(c)},319:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n(5),a=n(49),c=n(240),r=n(2);const s=Object(a.a)("header")((e=>{let{theme:t}=e;return{top:0,left:0,lineHeight:0,maxWidth:"600px",position:"absolute",padding:t.spacing(3,3,0),[t.breakpoints.up("sm")]:{padding:t.spacing(5,5,0)}}}));function o(){return Object(r.jsxs)(r.Fragment,{children:[Object(r.jsx)(s,{children:Object(r.jsx)(c.a,{disabledLink:!0})}),Object(r.jsx)(i.b,{})]})}},358:function(e,t,n){},36:function(e,t,n){"use strict";var i=n(330),a=n.n(i),c=n(237);const r=a.a.create({baseURL:c.b});r.interceptors.request.use((e=>{const t=localStorage.getItem("accessToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e}),(e=>Promise.reject(e))),r.interceptors.response.use((e=>e),(e=>Promise.reject(e.response&&e.response.data||"Something went wrong"))),t.a=r},368:function(e,t,n){},369:function(e,t,n){},394:function(e,t){},396:function(e,t){},408:function(e,t){},410:function(e,t){},438:function(e,t){},440:function(e,t){},441:function(e,t){},446:function(e,t){},448:function(e,t){},454:function(e,t){},456:function(e,t){},475:function(e,t){},487:function(e,t){},490:function(e,t){},515:function(e,t,n){"use strict";n.r(t);n(354),n(355),n(356),n(357),n(358);var i=n(52),a=n.n(i),c=n(43),r=n(234),s=n(134),o=n(328);var l=e=>{e&&e instanceof Function&&n.e(48).then(n.bind(null,1383)).then((t=>{let{getCLS:n,getFID:i,getFCP:a,getLCP:c,getTTFB:r}=t;n(e),i(e),a(e),c(e),r(e)}))},d=(n(368),n(369),n(90)),u=n(174),j=n(8),b=n(0),h=n(154),O=n(2);const f={collapseClick:!1,collapseHover:!1,onToggleCollapse:()=>{},onHoverEnter:()=>{},onHoverLeave:()=>{}},m=Object(b.createContext)(f);function p(e){let{children:t}=e;const n=Object(h.a)("up","lg"),[i,a]=Object(b.useState)({click:!1,hover:!1});Object(b.useEffect)((()=>{n||a({click:!1,hover:!1})}),[n]);return Object(O.jsx)(m.Provider,{value:{isCollapse:i.click&&!i.hover,collapseClick:i.click,collapseHover:i.hover,onToggleCollapse:()=>{a(Object(j.a)(Object(j.a)({},i),{},{click:!i.click}))},onHoverEnter:()=>{i.click&&a(Object(j.a)(Object(j.a)({},i),{},{hover:!0}))},onHoverLeave:()=>{a(Object(j.a)(Object(j.a)({},i),{},{hover:!1}))}},children:t})}var g=n(564),x=n(557),y=n(340),v=n(508),w=n(563),z=n(566);function k(e,t){return"linear-gradient(to bottom, ".concat(e,", ").concat(t,")")}const F={lighter:"#2ee7ff",light:"#38e8ff",main:"#33848f",dark:"#01060a",darker:"#00060a"},S={lighter:"#D0F2FF",light:"#74CAFF",main:"#1890FF",dark:"#0C53B7",darker:"#04297A"},P={lighter:"#E9FCD4",light:"#AAF27F",main:"#54D62C",dark:"#229A16",darker:"#08660D"},A={lighter:"#FFF7CD",light:"#FFE16A",main:"#FFC107",dark:"#B78103",darker:"#7A4F01"},I={lighter:"#FFE7D9",light:"#FFA48D",main:"#FF4842",dark:"#B72136",darker:"#7A0C2E"},L={0:"#FFFFFF",100:"#38e8ff",200:"#38B1FF",300:"#D0F2FF",400:"#004F99",500:"#38e8ff",600:"#061C2A",700:"#0a1217",800:"#040e16",900:"#00060a",5008:Object(z.a)("#38e8ff",.08),50012:Object(z.a)("#38e8ff",.12),50016:Object(z.a)("#38e8ff",.16),50024:Object(z.a)("#38e8ff",.24),50032:Object(z.a)("#38e8ff",.32),50048:Object(z.a)("#38e8ff",.48),50056:Object(z.a)("#38e8ff",.56),50080:Object(z.a)("#38e8ff",.8)},E={primary:k(F.light,F.main),info:k(S.light,S.main),success:k(P.light,P.main),warning:k(A.light,A.main),error:k(I.light,I.main)},T={common:{black:"#000",white:"#f0f0f0"},primary:Object(j.a)(Object(j.a)({},F),{},{contrastText:"#f0f0f0"}),secondary:Object(j.a)(Object(j.a)({},{lighter:"#D6E4FF",light:"#84A9FF",main:"#3366FF",dark:"#1939B7",darker:"#091A7A"}),{},{contrastText:"#f0f0f0"}),info:Object(j.a)(Object(j.a)({},S),{},{contrastText:"#f0f0f0"}),success:Object(j.a)(Object(j.a)({},P),{},{contrastText:L[800]}),warning:Object(j.a)(Object(j.a)({},A),{},{contrastText:L[800]}),error:Object(j.a)(Object(j.a)({},I),{},{contrastText:"#f0f0f0"}),grey:L,gradients:E,divider:L[50024],action:{hover:L[700],selected:L[700],disabled:L[50080],disabledBackground:L[50024],focus:L[700],hoverOpacity:.8,disabledOpacity:.48}};var C={dark:Object(j.a)(Object(j.a)({},T),{},{mode:"dark",text:{primary:"#f0f0f0",secondary:"#f0f0f0",disabled:"#a3a3a3"},background:{paper:"#000000",default:"#000000",neutral:"#262626"},action:Object(j.a)({active:"#737373"},T.action)})};n(124);function H(e){return"".concat(e/16,"rem")}function N(e){let{sm:t,md:n,lg:i}=e;return{"@media (min-width:600px)":{fontSize:H(t)},"@media (min-width:900px)":{fontSize:H(n)},"@media (min-width:1200px)":{fontSize:H(i)}}}const W="Orbitron, monospace",D="Roboto Mono, monospace";var B={fontFamily:"Public Sans, sans-serif",fontWeightRegular:400,fontWeightMedium:600,fontWeightBold:700,h1:Object(j.a)({fontWeight:700,lineHeight:1.25,fontSize:H(40),letterSpacing:2},N({sm:52,md:58,lg:64})),h2:Object(j.a)({fontWeight:700,lineHeight:64/48,fontSize:H(32)},N({sm:40,md:44,lg:48})),h3:Object(j.a)({fontWeight:700,lineHeight:1.5,fontSize:H(24)},N({sm:26,md:30,lg:32})),h4:Object(j.a)({fontWeight:700,lineHeight:1.5,fontSize:H(20)},N({sm:20,md:24,lg:24})),h5:Object(j.a)({fontWeight:700,lineHeight:1.5,fontSize:H(18)},N({sm:19,md:20,lg:20})),h6:Object(j.a)({fontWeight:700,lineHeight:28/18,fontSize:H(17)},N({sm:18,md:18,lg:18})),subtitle1:{fontWeight:600,lineHeight:1.5,fontSize:H(16)},subtitle2:{fontWeight:600,lineHeight:22/14,fontSize:H(14)},body1:{lineHeight:1.5,fontSize:H(16)},body2:{lineHeight:22/14,fontSize:H(14)},caption:{lineHeight:1.5,fontSize:H(12)},overline:{fontWeight:700,lineHeight:1.5,fontSize:H(12),textTransform:"uppercase"},button:{fontWeight:700,lineHeight:24/14,fontSize:H(14),textTransform:"capitalize"},digitalDisplay:{fontFamily:W,fontWeight:600,fontSize:H(16),letterSpacing:"0.1em",lineHeight:1.4},automotiveData:{fontFamily:D,fontWeight:500,fontSize:H(14),letterSpacing:"0.05em",lineHeight:1.3},automotiveLabel:{fontFamily:D,fontWeight:400,fontSize:H(14),letterSpacing:"0.05em",lineHeight:1.3},technicalInfo:{fontFamily:D,fontWeight:400,fontSize:H(12),letterSpacing:"0.05em",lineHeight:1.2},carStatus:{fontFamily:W,fontWeight:700,fontSize:H(18),letterSpacing:"0.15em",lineHeight:1.5}};var G={values:{xs:350,sm:600,md:900,lg:1200,xl:1536}};function R(e){let{children:t}=e;const n=Object(b.useMemo)((()=>({palette:C.dark,typography:B,breakpoints:G,shape:{borderRadius:8}})),[]),i=Object(y.a)(n);return Object(O.jsx)(v.a,{injectFirst:!0,children:Object(O.jsxs)(w.a,{theme:i,children:[Object(O.jsx)(x.a,{}),t]})})}var q=n(231);var V=e=>{let{children:t}=e;return Object(O.jsx)(q.a,{maxSnack:3,children:t})},U=n(561);var M=e=>{let{children:t}=e;return Object(O.jsx)(U.a.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:t})},Z=n(5),_=n(71);function J(e){let{children:t}=e;const{isAuthenticated:n}=Object(_.a)();return n?Object(O.jsx)(Z.a,{to:"/home"}):Object(O.jsx)(O.Fragment,{children:t})}const K=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(17)]).then(n.bind(null,1418)))),Y=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(2),n.e(5),n.e(20),n.e(42)]).then(n.bind(null,1429)))),$=Object(b.lazy)((()=>n.e(41).then(n.bind(null,1390))));var Q={path:"auth",children:[{path:"login",element:Object(O.jsx)(J,{children:Object(O.jsx)(K,{})})},{path:"verify",element:Object(O.jsxs)(J,{children:[" ",Object(O.jsx)(Y,{})]})},{path:"forgot-password",element:Object(O.jsxs)(J,{children:[" ",Object(O.jsx)($,{})]})}]},X=n(565);var ee=()=>Object(O.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"},children:Object(O.jsx)(X.a,{})});function te(e){let{children:t}=e;const{isAuthenticated:n,isInitialized:i}=Object(_.a)(),{pathname:a}=Object(Z.j)(),[c,r]=Object(b.useState)(null);return i?n?c&&a!==c?(r(null),Object(O.jsx)(Z.a,{to:c})):Object(O.jsx)(O.Fragment,{children:t}):(a!==c&&r(a),Object(O.jsx)(Z.a,{to:"/auth/login"})):Object(O.jsx)(ee,{})}const ne=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(21)]).then(n.bind(null,1410)))),ie=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(4),n.e(5),n.e(39)]).then(n.bind(null,1415)))),ae=Object(b.lazy)((()=>n.e(46).then(n.t.bind(null,1391,7)))),ce=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(32)]).then(n.bind(null,1392)))),re=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(4),n.e(37),n.e(43)]).then(n.bind(null,1393)))),se=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(15)]).then(n.bind(null,1394)))),oe=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(11)]).then(n.bind(null,1430)))),le=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(16)]).then(n.bind(null,1419)))),de=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(36)]).then(n.bind(null,1395)))),ue=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(34)]).then(n.bind(null,1396)))),je=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(29)]).then(n.bind(null,1422)))),be=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(30)]).then(n.bind(null,1397)))),he=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(31)]).then(n.bind(null,1398)))),Oe=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(25)]).then(n.bind(null,1399)))),fe=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(9)]).then(n.bind(null,1400)))),me=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(33)]).then(n.bind(null,1401)))),pe=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(23)]).then(n.bind(null,1414)))),ge=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(24)]).then(n.bind(null,1402)))),xe=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(35)]).then(n.bind(null,1416)))),ye=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(38)]).then(n.bind(null,1403))));var ve={path:"",children:[{element:Object(O.jsx)(ie,{}),index:!0},{path:"home",element:Object(O.jsxs)(te,{children:[" ",Object(O.jsx)(ne,{})," "]})},{path:"help",element:Object(O.jsx)(re,{})},{path:"device-register",element:Object(O.jsxs)(te,{children:[" ",Object(O.jsx)(fe,{})," "]})},{path:"license-profile",element:Object(O.jsxs)(te,{children:[" ",Object(O.jsx)(he,{})," "]})},{path:"pin-code",element:Object(O.jsxs)(te,{children:[" ",Object(O.jsx)(ae,{})," "]})},{path:"time-command",element:Object(O.jsxs)(te,{children:[" ",Object(O.jsx)(ce,{})," "]})},{path:"log-management",element:Object(O.jsxs)(te,{children:[" ",Object(O.jsx)(se,{})," "]})},{path:"log-map",element:Object(O.jsxs)(te,{children:[" ",Object(O.jsx)(de,{})," "]})},{path:"log-sim",element:Object(O.jsxs)(te,{children:[" ",Object(O.jsx)(ue,{})," "]})},{path:"log-gps",element:Object(O.jsxs)(te,{children:[" ",Object(O.jsx)(je,{})," "]})},{path:"configure-driver",element:Object(O.jsxs)(te,{children:[" ",Object(O.jsx)(be,{})," "]})},{path:"configure-gps/:id",element:Object(O.jsxs)(te,{children:[" ",Object(O.jsx)(me,{})," "]})},{path:"log-license",element:Object(O.jsxs)(te,{children:[" ",Object(O.jsx)(oe,{})," "]})},{path:"Order",element:Object(O.jsxs)(te,{children:[" ",Object(O.jsx)(pe,{})," "]})},{path:"driver-profile",element:Object(O.jsxs)(te,{children:[" ",Object(O.jsx)(Oe,{})," "]})},{path:"notification",element:Object(O.jsxs)(te,{children:[" ",Object(O.jsx)(le,{})," "]})},{path:"device-config/:id",element:Object(O.jsx)(ge,{})},{path:"mqtt-test",element:Object(O.jsx)(xe,{})},{path:"mqtt-tcp-test",element:Object(O.jsx)(xe,{})},{path:"paho-mqtt",element:Object(O.jsx)(ye,{})}]};function we(e){var t;let{children:n}=e;const{isAuthenticated:i,isInitialized:a,user:c}=Object(_.a)();return a?i?null!==c&&void 0!==c&&null!==(t=c.role)&&void 0!==t&&t.includes("admin")?Object(O.jsx)(O.Fragment,{children:n}):Object(O.jsx)(Z.a,{to:"/"}):Object(O.jsx)(Z.a,{to:"/auth/login"}):Object(O.jsx)(ee,{})}const ze=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(10)]).then(n.bind(null,1409)))),ke=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(26)]).then(n.bind(null,1404)))),Fe=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(12)]).then(n.bind(null,1405)))),Se=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(13)]).then(n.bind(null,1406)))),Pe=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(14)]).then(n.bind(null,1431)))),Ae=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(27)]).then(n.bind(null,1420)))),Ie=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(19)]).then(n.bind(null,1411)))),Le=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(22)]).then(n.bind(null,1412))));var Ee={path:"admin",children:[{path:"device/:id",element:Object(O.jsx)(te,{children:Object(O.jsx)(we,{children:Object(O.jsx)(ze,{})})})},{path:"user-manage",element:Object(O.jsx)(te,{children:Object(O.jsx)(we,{children:Object(O.jsx)(ke,{})})})},{path:"orders",element:Object(O.jsx)(te,{children:Object(O.jsx)(we,{children:Object(O.jsx)(Pe,{})})})},{path:"transactions",element:Object(O.jsx)(te,{children:Object(O.jsx)(we,{children:Object(O.jsx)(Fe,{})})})},{path:"withdraw-requests",element:Object(O.jsx)(te,{children:Object(O.jsx)(we,{children:Object(O.jsx)(Se,{})})})},{path:"app-management",element:Object(O.jsx)(te,{children:Object(O.jsx)(we,{children:Object(O.jsx)(Ae,{})})})},{path:"statistics",element:Object(O.jsx)(te,{children:Object(O.jsx)(we,{children:Object(O.jsx)(Ie,{})})})},{path:"income",element:Object(O.jsx)(te,{children:Object(O.jsx)(we,{children:Object(O.jsx)(Le,{})})})}]};function Te(e){let{children:t}=e;const{isAuthenticated:n,isInitialized:i,user:a}=Object(_.a)();return i?n?"installer"!==(null===a||void 0===a?void 0:a.role)?Object(O.jsx)(Z.a,{to:"/"}):Object(O.jsx)(O.Fragment,{children:t}):Object(O.jsx)(Z.a,{to:"/auth/login"}):Object(O.jsx)(ee,{})}const Ce=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(2),n.e(3),n.e(28)]).then(n.bind(null,1407))));var He={path:"installer",children:[{path:"",element:Object(O.jsx)(Z.a,{to:"/installer/dashboard",replace:!0})},{path:"dashboard",element:Object(O.jsx)(te,{children:Object(O.jsx)(Te,{children:Object(O.jsx)(Ce,{})})})}]},Ne=n(319);const We=Object(b.lazy)((()=>Promise.all([n.e(0),n.e(1),n.e(4),n.e(40)]).then(n.bind(null,1413))));var De={path:"*",element:Object(O.jsx)(Ne.a,{}),children:[{path:"404",element:Object(O.jsx)(We,{})},{path:"*",element:Object(O.jsx)(Z.a,{to:"/404",replace:!0})}]};function Be(){return Object(Z.p)([Q,ve,Ee,He,De])}var Ge=function(){return Object(O.jsx)("footer",{style:{display:"flex",justifyContent:"center",alignItems:"center",padding:"10px"},children:Object(O.jsx)("p",{style:{margin:"0 10px"},children:"\xa9 2025 Elec.mn All Rights Reserved."})})};function Re(){const{i18n:e}=Object(g.a)(),t=localStorage.getItem("language")||"en";return Object(b.useEffect)((()=>{e.changeLanguage(t)}),[t,e]),Object(O.jsxs)(R,{children:[Object(O.jsx)(V,{children:Object(O.jsx)(M,{children:Object(O.jsx)(b.Suspense,{fallback:Object(O.jsx)(ee,{}),children:Object(O.jsx)(Be,{})})})}),Object(O.jsx)(Ge,{})]})}const qe=Boolean("localhost"===window.location.hostname||"[::1]"===window.location.hostname||window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));function Ve(e,t){navigator.serviceWorker.register(e).then((e=>{e.onupdatefound=()=>{const n=e.installing;null!=n&&(n.onstatechange=()=>{"installed"===n.state&&(navigator.serviceWorker.controller?(console.log("New content is available and will be used when all tabs for this page are closed. See https://cra.link/PWA."),t&&t.onUpdate&&t.onUpdate(e)):(console.log("Content is cached for offline use."),t&&t.onSuccess&&t.onSuccess(e)))})}})).catch((e=>{console.error("Error during service worker registration:",e)}))}var Ue=n(213),Me=n(337),Ze=n(339),_e=n(141);Ue.a.use(Me.a).use(Ze.a).use(_e.e).init({debug:!1,fallbackLng:"en",lng:"mn",whitelist:["mn","en","ru"],interpolation:{escapeValue:!1},react:{useSuspense:!1}});Ue.a;a.a.render(Object(O.jsx)(u.b,{children:Object(O.jsx)(r.b,{children:Object(O.jsx)(s.a,{store:d.c,children:Object(O.jsx)(o.PersistGate,{loading:null,persistor:d.b,children:Object(O.jsx)(p,{children:Object(O.jsx)(c.a,{children:Object(O.jsx)(Re,{})})})})})})}),document.getElementById("root")),function(e){if("serviceWorker"in navigator){if(new URL("",window.location.href).origin!==window.location.origin)return;window.addEventListener("load",(()=>{const t="".concat("","/service-worker.js");qe?(!function(e,t){fetch(e,{headers:{"Service-Worker":"script"}}).then((n=>{const i=n.headers.get("content-type");404===n.status||null!=i&&-1===i.indexOf("javascript")?navigator.serviceWorker.ready.then((e=>{e.unregister().then((()=>{window.location.reload()}))})):Ve(e,t)})).catch((()=>{console.log("No internet connection found. App is running in offline mode.")}))}(t,e),navigator.serviceWorker.ready.then((()=>{console.log("This web app is being served cache-first by a service worker. To learn more, visit https://cra.link/PWA")}))):Ve(t,e)}))}}(),l()},71:function(e,t,n){"use strict";var i=n(0),a=n(174);t.a=()=>{const e=Object(i.useContext)(a.a);if(!e)throw new Error("Auth context must be use inside AuthProvider");return e}},90:function(e,t,n){"use strict";n.d(t,"c",(function(){return f})),n.d(t,"b",(function(){return m})),n.d(t,"a",(function(){return p}));var i=n(108),a=n(134),c=n(226),r=n(29),s=n(329),o=n.n(s);const l=Object(i.b)({name:"car",initialState:{isLoading:!1,error:null,status:null,routines:null},reducers:{startLoading(e){e.isLoading=!0},hasError(e,t){e.error=t.payload,e.isLoading=!1},setCarStatus(e,t){e.isLoading=!1,e.status=t.payload},setRoutines(e,t){e.routines=t.payload}}});var d=l.reducer;const{setCarStatus:u,setRoutines:j}=l.actions;var b=n(244);const h={key:"root",storage:o.a,keyPrefix:"redux-",whitelist:["car"]},O=Object(r.b)({car:d,notification:b.a}),f=Object(i.a)({reducer:Object(c.a)(h,O),middleware:e=>e({serializableCheck:!1,immutableCheck:!1})}),m=Object(c.b)(f),{dispatch:p}=f;a.c}},[[515,7,8]]]);
//# sourceMappingURL=main.3c81d295.chunk.js.map