
// components
import Iconify from '../components/Iconify';

// ----------------------------------------------------------------------

const ICON_SIZE = {
  width: 22,
  height: 22,
};

const menuConfig = [
  {
    title: 'Home',
    icon: <Iconify icon={'eva:home-fill'} {...ICON_SIZE} />,
    path: '/home',
  },
  {
    title: 'MQTT Test',
    icon: <Iconify icon={'mdi:connection'} {...ICON_SIZE} />,
    path: '/mqtt-test',
  },
  {
    title: 'App Management',
    icon: <Iconify icon={'mdi:cellphone-cog'} {...ICON_SIZE} />,
    path: '/admin/app-management',
    roles: ['admin'], // This item will only be shown to admin users
  },
  {
    title: 'Usage Statistics',
    icon: <Iconify icon={'mdi:chart-bar'} {...ICON_SIZE} />,
    path: '/admin/statistics',
    roles: ['admin'], // This item will only be shown to admin users
  },
  {
    title: 'Income Monitoring',
    icon: <Iconify icon={'mdi:cash-multiple'} {...ICON_SIZE} />,
    path: '/admin/income',
    roles: ['admin'], // This item will only be shown to admin users
  },
];

export default menuConfig;
